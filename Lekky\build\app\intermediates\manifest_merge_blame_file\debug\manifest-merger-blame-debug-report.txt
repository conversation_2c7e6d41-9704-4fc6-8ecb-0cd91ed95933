1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.lekky.app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!-- Add permissions for notifications -->
17    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Add permission for boot completed to reschedule notifications -->
17-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:4:5-76
17-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:4:22-74
18    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /> <!-- Add permissions for storage access -->
18-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:6:5-80
18-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:6:22-78
19    <uses-permission
19-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:8:5-106
20        android:name="android.permission.READ_EXTERNAL_STORAGE"
20-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:8:22-77
21        android:maxSdkVersion="32" />
21-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:8:78-104
22    <uses-permission
22-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:9:5-107
23        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
23-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:9:22-78
24        android:maxSdkVersion="29" />
24-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:9:79-105
25    <!--
26 Required to query activities that can process text, see:
27         https://developer.android.com/training/package-visibility?hl=en and
28         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
29
30         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
31    -->
32    <queries>
32-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:65:5-82:15
33        <intent>
33-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:66:9-69:18
34            <action android:name="android.intent.action.PROCESS_TEXT" />
34-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:67:13-72
34-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:67:21-70
35
36            <data android:mimeType="text/plain" />
36-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:68:13-50
36-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:68:19-48
37        </intent>
38        <!-- For document selection -->
39        <intent>
39-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:71:9-73:18
40            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
40-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:72:13-79
40-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:72:21-76
41        </intent>
42        <intent>
42-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:74:9-77:18
43            <action android:name="android.intent.action.CREATE_DOCUMENT" />
43-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:75:13-76
43-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:75:21-73
44
45            <data android:mimeType="text/csv" />
45-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:68:13-50
45-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:68:19-48
46        </intent>
47        <intent>
47-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:78:9-81:18
48            <action android:name="android.intent.action.OPEN_DOCUMENT" />
48-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:79:13-74
48-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:79:21-71
49
50            <data android:mimeType="text/csv" />
50-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:68:13-50
50-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:68:19-48
51        </intent>
52        <intent>
52-->[:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:12:9-16:18
53            <action android:name="android.intent.action.GET_CONTENT" />
53-->[:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-72
53-->[:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:13:21-69
54
55            <data android:mimeType="*/*" />
55-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:68:13-50
55-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:68:19-48
56        </intent>
57    </queries>
58
59    <uses-permission android:name="android.permission.VIBRATE" />
59-->[:flutter_local_notifications] D:\000.Workspace\Lekky\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-66
59-->[:flutter_local_notifications] D:\000.Workspace\Lekky\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:9:22-63
60
61    <permission
61-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
62        android:name="com.lekky.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
62-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
63        android:protectionLevel="signature" />
63-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
64
65    <uses-permission android:name="com.lekky.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
65-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
65-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
66
67    <application
68        android:name="android.app.Application"
69        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
69-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
70        android:debuggable="true"
71        android:enableOnBackInvokedCallback="true"
72        android:icon="@mipmap/ic_launcher"
73        android:label="lekky" >
74        <activity
75            android:name="com.lekky.app.MainActivity"
76            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
77            android:exported="true"
78            android:hardwareAccelerated="true"
79            android:launchMode="singleTop"
80            android:theme="@style/LaunchTheme"
81            android:windowSoftInputMode="adjustResize" >
82
83            <!--
84                 Specifies an Android theme to apply to this Activity as soon as
85                 the Android process has started. This theme is visible to the user
86                 while the Flutter UI initializes. After that, this theme continues
87                 to determine the Window background behind the Flutter UI.
88            -->
89            <meta-data
90                android:name="io.flutter.embedding.android.NormalTheme"
91                android:resource="@style/NormalTheme" />
92
93            <intent-filter>
94                <action android:name="android.intent.action.MAIN" />
95
96                <category android:name="android.intent.category.LAUNCHER" />
97            </intent-filter>
98        </activity>
99        <!--
100             Don't delete the meta-data below.
101             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
102        -->
103        <meta-data
104            android:name="flutterEmbedding"
105            android:value="2" />
106
107        <!-- Add receiver for boot completed to reschedule notifications -->
108        <receiver
109            android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver"
110            android:exported="true" >
111            <intent-filter>
112                <action android:name="android.intent.action.BOOT_COMPLETED" />
113                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
114            </intent-filter>
115        </receiver>
116
117        <!-- Package name for the application -->
118        <meta-data
119            android:name="com.lekky.app.PACKAGE_NAME"
120            android:value="com.lekky.app" />
121
122        <activity
122-->[:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-13:74
123            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
123-->[:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-74
124            android:exported="false"
124-->[:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-37
125            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
125-->[:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-71
126
127        <uses-library
127-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:25:9-27:40
128            android:name="androidx.window.extensions"
128-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:26:13-54
129            android:required="false" />
129-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:27:13-37
130        <uses-library
130-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:28:9-30:40
131            android:name="androidx.window.sidecar"
131-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:29:13-51
132            android:required="false" />
132-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:30:13-37
133    </application>
134
135</manifest>
