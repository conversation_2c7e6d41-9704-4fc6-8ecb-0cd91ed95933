// File: lib/core/shared_modules/data_import_service.dart
import 'dart:async';
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import '../di/service_locator.dart';
import '../../features/backup/backup_service.dart';
import '../../features/backup/presentation/dialogs/import_strategy_dialog.dart';
import '../../features/data_management/data/csv_parser.dart';
import '../../features/data_management/domain/import_validator.dart';
import '../../features/data_management/presentation/screens/import_screen.dart';
import '../../features/meter_readings/domain/models/meter_reading.dart';
import '../../features/top_ups/domain/models/top_up.dart';
import '../../features/meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../features/top_ups/domain/repositories/top_up_repository.dart';
import '../models/meter_entry.dart';
import '../utils/app_error.dart';
import '../utils/error_types.dart';
import '../utils/bulk_operation_context.dart';
import '../utils/performance_monitor.dart';
import '../utils/logger.dart';
import '../utils/result.dart';
import '../utils/event_bus.dart';
import '../../features/averages/domain/services/average_service.dart';
import '../../features/validation/domain/services/validation_trigger_service.dart';

/// Simple adapter to replace HistoryController for import operations
class HistoryControllerAdapter {
  final MeterReadingRepository _meterReadingRepository;
  final TopUpRepository _topUpRepository;

  HistoryControllerAdapter({
    required MeterReadingRepository meterReadingRepository,
    required TopUpRepository topUpRepository,
  })  : _meterReadingRepository = meterReadingRepository,
        _topUpRepository = topUpRepository;

  /// Get all entries (both meter readings and top-ups)
  Future<List<dynamic>> getAllEntries() async {
    final meterReadings = await _meterReadingRepository.getAllMeterReadings();
    final topUps = await _topUpRepository.getAllTopUps();
    return [...meterReadings, ...topUps];
  }

  /// Bulk add entries with progress callback
  Future<bool> bulkAddEntries(
    List<MeterEntry> entries, {
    bool replace = false,
    Function(double)? onProgress,
  }) async {
    return await PerformanceMonitor.timeOperation('bulk_import', () async {
      try {
        // Start bulk operation to suppress individual events
        BulkOperationContext.startBulkOperation();

        onProgress?.call(0.1);

        if (replace) {
          // Delete all existing entries
          final allMeterReadings =
              await _meterReadingRepository.getAllMeterReadings();
          final allTopUps = await _topUpRepository.getAllTopUps();

          for (final reading in allMeterReadings) {
            if (reading.id != null) {
              await _meterReadingRepository.deleteMeterReading(reading.id!);
            }
          }

          for (final topUp in allTopUps) {
            if (topUp.id != null) {
              await _topUpRepository.deleteTopUp(topUp.id!);
            }
          }

          onProgress?.call(0.4);
        }

        // Add new entries
        int count = 0;
        final totalCount = entries.length;

        for (final entry in entries) {
          if (entry.typeCode == 0) {
            // Meter reading
            final meterReading = MeterReading(
              value: entry.reading,
              date: entry.date,
              notes: entry.notes,
            );
            await _meterReadingRepository.addMeterReading(meterReading);
          } else if (entry.typeCode == 1) {
            // Top-up
            final topUp = TopUp(
              amount: entry.amountToppedUp,
              date: entry.date,
              notes: entry.notes,
            );
            await _topUpRepository.addTopUp(topUp);
          }

          count++;
          onProgress?.call(0.4 + (count / totalCount) * 0.6);
        }

        // End bulk operation and fire single event
        BulkOperationContext.endBulkOperation();

        // Update averages once after all entries are imported
        // AverageService will fire EventType.dataUpdated when calculation completes
        Logger.info(
            'HistoryControllerAdapter: Starting average calculation after import');
        final averageService = serviceLocator<AverageService>();
        await averageService.updateAverages();
        Logger.info(
            'HistoryControllerAdapter: Average calculation completed after import');

        // Trigger validation after import
        Logger.info(
            'HistoryControllerAdapter: Starting validation after import');
        final validationTriggerService =
            serviceLocator<ValidationTriggerService>();
        await validationTriggerService.validateAfterImport();
        Logger.info(
            'HistoryControllerAdapter: Validation completed after import');

        // Give a small delay to ensure all async operations complete
        await Future.delayed(const Duration(milliseconds: 100));

        // Fire an additional event to ensure all providers refresh
        Logger.info(
            'HistoryControllerAdapter: Firing additional dataUpdated event');
        EventBus().fire(EventType.dataUpdated);

        Logger.info(
            'HistoryControllerAdapter: Completed bulk add of ${BulkOperationContext.operationCount} entries');

        return true;
      } catch (e) {
        // Reset bulk operation context on error
        BulkOperationContext.reset();
        Logger.error('Failed to bulk add entries: $e');
        return false;
      }
    });
  }

  /// Replace all entries
  Future<bool> replaceAllEntries(
    List<MeterEntry> entries, {
    Function(double)? onProgress,
  }) async {
    return await bulkAddEntries(entries, replace: true, onProgress: onProgress);
  }
}

/// A shared service for importing data from backup files
/// Can be used by both Welcome screen and Settings screen
class DataImportService {
  final BackupService _backupService;
  final CsvParser _csvParser;
  final ImportValidator _importValidator;
  final logger = Logger('DataImportService');

  DataImportService({
    BackupService? backupService,
    CsvParser? csvParser,
    ImportValidator? importValidator,
  })  : _backupService = backupService ?? BackupService(),
        _csvParser = csvParser ?? CsvParser(),
        _importValidator = importValidator ?? ImportValidator();

  /// Check if a backup file exists in the standard location
  Future<bool> backupFileExists() async {
    return await _backupService.backupFileExists();
  }

  /// Get the path to the backup file
  Future<String?> getBackupFilePath() async {
    return await _backupService.getBackupFilePath();
  }

  /// Import data from the standard backup file location
  /// Used by the Welcome screen
  Future<Result<List<MeterEntry>>> importFromBackupFile() async {
    try {
      // Check if backup file exists
      if (!await _backupService.backupFileExists()) {
        return Result.failure(AppError(
          message: 'No backup file found',
          type: ErrorType.notFound,
          severity: ErrorSeverity.medium,
        ));
      }

      // Get the backup file path
      final path = await _backupService.getBackupFilePath();
      if (path == null) {
        return Result.failure(AppError(
          message: 'Could not access backup file path',
          type: ErrorType.permission,
          severity: ErrorSeverity.medium,
        ));
      }

      final file = File(path);

      // Import entries using the backup service
      return await _backupService.importMeterEntries(file);
    } catch (e) {
      logger.e('Failed to import from backup file', details: e.toString());
      return Result.failure(AppError(
        message: 'Failed to import data: $e',
        type: ErrorType.unknown,
        severity: ErrorSeverity.high,
      ));
    }
  }

  /// Import data from a user-selected file
  /// Used by the Settings screen and Welcome screen
  /// If skipStrategyDialog is true, automatically uses replace strategy
  Future<Result<List<MeterEntry>>> importFromUserSelectedFile(
      BuildContext context,
      {bool skipStrategyDialog = false}) async {
    try {
      // Pick a file
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv'],
        allowMultiple: false,
        dialogTitle: 'Select CSV file',
        withData: true,
      );

      if (result == null || result.files.isEmpty) {
        return Result.failure(AppError(
          message: 'No file selected',
          type: ErrorType.userCancelled,
          severity: ErrorSeverity.low,
        ));
      }

      File csvFile;
      // Check if we have file bytes (for Android)
      if (result.files.first.bytes != null) {
        // Create a temporary file from bytes
        final tempDir = await getTemporaryDirectory();
        csvFile = File('${tempDir.path}/temp_import.csv');
        await csvFile.writeAsBytes(result.files.first.bytes!);
      }
      // Otherwise try to read from path (for iOS)
      else if (result.files.first.path != null) {
        csvFile = File(result.files.first.path!);
      } else {
        return Result.failure(AppError(
          message: 'Could not read file data',
          type: ErrorType.fileIOError,
          severity: ErrorSeverity.medium,
        ));
      }

      // Detect file format and parse accordingly
      if (!context.mounted) {
        return Result.failure(AppError(
          message: 'Context not mounted',
          type: ErrorType.validation,
          severity: ErrorSeverity.medium,
        ));
      }
      final parseResult = await _parseFileWithFormatDetection(csvFile, context);
      if (parseResult.isFailure) {
        return parseResult;
      }

      final entries = parseResult.value;

      // Determine import strategy
      ImportStrategy strategy;
      if (skipStrategyDialog) {
        // For welcome screen - automatically use replace strategy
        strategy = ImportStrategy.replace;
      } else {
        // Show import strategy dialog for settings screen
        if (context.mounted) {
          final selectedStrategy = await showDialog<ImportStrategy>(
            context: context,
            builder: (context) => ImportStrategyDialog(entries: entries),
          );

          if (selectedStrategy == null ||
              selectedStrategy == ImportStrategy.cancel) {
            return Result.failure(AppError(
              message: 'Import was cancelled',
              type: ErrorType.userCancelled,
              severity: ErrorSeverity.low,
            ));
          }
          strategy = selectedStrategy;
        } else {
          return Result.failure(AppError(
            message: 'Context is no longer valid',
            type: ErrorType.unknown,
            severity: ErrorSeverity.medium,
          ));
        }
      }

      // Continue with import process

      // Get the repositories
      final meterReadingRepository = serviceLocator<MeterReadingRepository>();
      final topUpRepository = serviceLocator<TopUpRepository>();

      // Create the history controller adapter
      final historyController = HistoryControllerAdapter(
        meterReadingRepository: meterReadingRepository,
        topUpRepository: topUpRepository,
      );

      // Show the import screen with selected strategy
      List<MeterEntry>? importedEntries;

      // Use a Completer to handle the async result properly
      final completer = Completer<List<MeterEntry>?>();

      // Schedule the navigation for the next frame to avoid context issues
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (context.mounted) {
          Navigator.of(context)
              .push(
            MaterialPageRoute(
              builder: (context) => ImportScreen(
                file: csvFile,
                historyController: historyController,
                replaceExistingData: strategy == ImportStrategy.replace,
                preParsedEntries: entries,
                onComplete: (entries) {
                  completer.complete(entries);
                },
              ),
            ),
          )
              .then((_) {
            if (!completer.isCompleted) {
              completer.complete(null);
            }
          });
        } else {
          completer.complete(null);
        }
      });

      // Wait for the import to complete
      importedEntries = await completer.future;

      if (importedEntries != null) {
        return Result.success(importedEntries);
      } else {
        return Result.failure(AppError(
          message: 'Import was cancelled',
          type: ErrorType.userCancelled,
          severity: ErrorSeverity.low,
        ));
      }
    } catch (e) {
      logger.e('Failed to import from user-selected file',
          details: e.toString());
      return Result.failure(AppError(
        message: 'Failed to import data: $e',
        type: ErrorType.unknown,
        severity: ErrorSeverity.high,
      ));
    }
  }

  /// Import data from the Downloads folder
  /// This method will first look for the standard backup file in the Downloads folder
  /// If not found, it will prompt the user to select a file
  Future<Result<List<MeterEntry>>> importFromDownloadsFolder(
      BuildContext context) async {
    try {
      // First try to find the file in the Downloads folder
      final backupExists = await _backupService.backupFileExists();
      if (backupExists) {
        final path = await _backupService.getBackupFilePath();
        if (path != null) {
          final file = File(path);
          logger.i('Found backup file in Downloads folder: $path');

          // Detect file format and parse accordingly
          if (!context.mounted) {
            return Result.failure(AppError(
              message: 'Context not mounted',
              type: ErrorType.validation,
              severity: ErrorSeverity.medium,
            ));
          }
          final parseResult =
              await _parseFileWithFormatDetection(file, context);
          if (parseResult.isFailure) {
            return parseResult;
          }

          final entries = parseResult.value;

          // Show import strategy dialog
          if (context.mounted) {
            final strategy = await showDialog<ImportStrategy>(
              context: context,
              builder: (context) => ImportStrategyDialog(entries: entries),
            );

            if (strategy == null || strategy == ImportStrategy.cancel) {
              return Result.failure(AppError(
                message: 'Import was cancelled',
                type: ErrorType.userCancelled,
                severity: ErrorSeverity.low,
              ));
            }

            // Get the repositories
            final meterReadingRepository =
                serviceLocator<MeterReadingRepository>();
            final topUpRepository = serviceLocator<TopUpRepository>();

            // Create the history controller adapter
            final historyController = HistoryControllerAdapter(
              meterReadingRepository: meterReadingRepository,
              topUpRepository: topUpRepository,
            );

            // Show the import screen with selected strategy
            List<MeterEntry>? importedEntries;

            if (context.mounted) {
              await Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => ImportScreen(
                    file: file,
                    historyController: historyController,
                    replaceExistingData: strategy == ImportStrategy.replace,
                    preParsedEntries: entries,
                    onComplete: (entries) {
                      importedEntries = entries;
                    },
                  ),
                ),
              );
            }

            if (importedEntries != null) {
              return Result.success(importedEntries!);
            }
          }
        }
      }

      // Check if context is still valid
      if (!context.mounted) {
        return Result.failure(AppError(
          message: 'Context is no longer valid',
          type: ErrorType.unknown,
          severity: ErrorSeverity.medium,
        ));
      }

      // If not found, prompt the user to select a file
      logger.i(
          'No backup file found in Downloads folder, asking user to select a file');
      return await importFromUserSelectedFile(context);
    } catch (e) {
      logger.e('Failed to import from Downloads folder', details: e.toString());
      return Result.failure(AppError(
        message: 'Failed to import data: $e',
        type: ErrorType.unknown,
        severity: ErrorSeverity.high,
      ));
    }
  }

  /// Save imported entries to the database
  /// This method handles both replacing existing data and appending to it
  Future<Result<int>> saveImportedEntries({
    required List<MeterEntry> entries,
    required HistoryControllerAdapter historyController,
    required bool replaceExistingData,
    Function(double)? onProgress,
  }) async {
    try {
      // Report initial progress
      onProgress?.call(0.1);

      // Add entries to the database
      final success = await historyController.bulkAddEntries(
        entries,
        replace: replaceExistingData,
        onProgress: onProgress,
      );

      if (!success) {
        return Result.failure(AppError(
          message: 'Failed to save imported entries to the database',
          type: ErrorType.database,
          severity: ErrorSeverity.high,
        ));
      }

      // Report completion
      onProgress?.call(1.0);

      return Result.success(entries.length);
    } catch (e) {
      logger.e('Failed to save imported entries', details: e.toString());
      return Result.failure(AppError(
        message: 'Failed to save imported entries: $e',
        type: ErrorType.database,
        severity: ErrorSeverity.high,
      ));
    }
  }

  /// Validate imported entries
  /// This method checks for potential issues with the imported data
  Future<List<String>> validateImportedEntries(List<MeterEntry> entries) async {
    return await _importValidator.validateEntries(entries);
  }

  /// Parse file with format detection
  /// Detects if file is Lekky backup format or generic CSV and routes to appropriate parser
  Future<Result<List<MeterEntry>>> _parseFileWithFormatDetection(
      File csvFile, BuildContext context) async {
    try {
      // Read first few lines to detect format
      final csvString = await csvFile.readAsString();
      final lines = csvString.split('\n');

      if (lines.isEmpty) {
        return Result.failure(AppError(
          message: 'The file is empty',
          type: ErrorType.invalidData,
          severity: ErrorSeverity.medium,
        ));
      }

      // Check for Lekky backup format header
      final versionHeader = lines.first.trim();
      final versionRegex = RegExp(r'# Lekky v[\d\.]+\s+BackupFormat=(\d+)');
      final isLekkyBackup = versionRegex.hasMatch(versionHeader);

      if (isLekkyBackup) {
        // Use BackupService for Lekky backup files
        return await _backupService.importMeterEntries(csvFile);
      } else {
        // Check if context is still mounted before showing dialog
        if (!context.mounted) {
          return Result.failure(AppError(
            message: 'Context is no longer valid',
            type: ErrorType.unknown,
            severity: ErrorSeverity.medium,
          ));
        }

        // Show warning dialog for generic CSV files
        final proceed = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Generic CSV File'),
            content: const Text(
                'This appears to be a generic CSV file, not a Lekky backup. '
                'The import may not work correctly.\n\n'
                'Do you want to proceed?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Proceed'),
              ),
            ],
          ),
        );

        if (proceed != true) {
          return Result.failure(AppError(
            message: 'Import was cancelled',
            type: ErrorType.userCancelled,
            severity: ErrorSeverity.low,
          ));
        }

        // Use CsvParser for generic CSV files
        return await _csvParser.parseFile(csvFile);
      }
    } catch (e) {
      logger.e('Failed to parse file with format detection',
          details: e.toString());
      return Result.failure(AppError(
        message: 'Failed to parse file: $e',
        type: ErrorType.fileIOError,
        severity: ErrorSeverity.high,
      ));
    }
  }
}
