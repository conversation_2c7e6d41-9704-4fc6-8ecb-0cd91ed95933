{"logs": [{"outputFile": "com.lekky.app-mergeDebugResources-13:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d8adab4e681d0863a25dfe4be8db95bf\\transformed\\jetified-window-1.0.0-beta04\\res\\values\\values.xml", "from": {"startLines": "2,7,8,9,10,11,19,23,34,51", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,234,294,346,391,451,869,1066,1790,2904", "endLines": "6,7,8,9,10,18,22,33,50,58", "endColumns": "11,59,51,44,59,24,24,24,24,24", "endOffsets": "229,289,341,386,446,864,1061,1785,2899,3292"}, "to": {"startLines": "3,8,9,10,87,146,152,265,273,285", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "115,294,354,406,5300,8956,9151,13047,13329,13769", "endLines": "7,8,9,10,87,151,155,272,284,292", "endColumns": "11,59,51,44,59,24,24,24,24,24", "endOffsets": "289,349,401,446,5355,9146,9277,13324,13764,14073"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4f3949bbe11b9a3df8476b3579cc79b0\\transformed\\browser-1.5.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "13,14,15,16,24,25,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "611,669,735,798,1356,1427,6973,7041,7108,7187", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "664,730,793,855,1422,1494,7036,7103,7182,7251"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5c275d7683859a49fefc7284389acf42\\transformed\\media-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,341,394,447,500,560,626,748,809,875", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "139,210,283,336,389,442,495,555,621,743,804,870,937"}, "to": {"startLines": "21,22,23,48,49,50,51,107,133,135,136,141,143", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1123,1212,1283,2965,3018,3071,3124,6332,7995,8171,8293,8555,8750", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "1207,1278,1351,3013,3066,3119,3172,6387,8056,8288,8349,8616,8812"}}, {"source": "D:\\000.Workspace\\Lekky\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "121,125", "startColumns": "4,4", "startOffsets": "7327,7508", "endLines": "124,127", "endColumns": "12,12", "endOffsets": "7503,7672"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5389d6eda3e5eba8ba922c509419fb7d\\transformed\\lifecycle-runtime-2.3.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "6289", "endColumns": "42", "endOffsets": "6327"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\684577351670909f117ab3c5c378ca3b\\transformed\\core-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "2,11,12,17,18,19,20,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,108,109,110,111,112,113,114,115,120,128,129,134,137,142,144,145,156,162,172,205,226,259", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,451,523,860,925,991,1060,1499,1569,1637,1709,1779,1840,1914,1987,2048,2109,2171,2235,2297,2358,2426,2526,2586,2652,2725,2794,2851,2903,3177,3249,3325,3390,3449,3508,3568,3628,3688,3748,3808,3868,3928,3988,4048,4108,4167,4227,4287,4347,4407,4467,4527,4587,4647,4707,4767,4826,4886,4946,5005,5064,5123,5182,5241,5360,5395,5430,5485,5548,5603,5661,5719,5780,5843,5900,5951,6001,6062,6119,6185,6219,6254,6392,6462,6529,6601,6670,6739,6813,6885,7256,7677,7794,8061,8354,8621,8817,8889,9282,9485,9786,11517,12198,12880", "endLines": "2,11,12,17,18,19,20,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,108,109,110,111,112,113,114,115,120,128,132,134,140,142,144,145,161,171,204,225,258,264", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,518,606,920,986,1055,1118,1564,1632,1704,1774,1835,1909,1982,2043,2104,2166,2230,2292,2353,2421,2521,2581,2647,2720,2789,2846,2898,2960,3244,3320,3385,3444,3503,3563,3623,3683,3743,3803,3863,3923,3983,4043,4103,4162,4222,4282,4342,4402,4462,4522,4582,4642,4702,4762,4821,4881,4941,5000,5059,5118,5177,5236,5295,5390,5425,5480,5543,5598,5656,5714,5775,5838,5895,5946,5996,6057,6114,6180,6214,6249,6284,6457,6524,6596,6665,6734,6808,6880,6968,7322,7789,7990,8166,8550,8745,8884,8951,9480,9781,11512,12193,12875,13042"}}]}]}