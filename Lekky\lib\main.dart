import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart' as provider;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'core/constants/app_constants.dart';
import 'core/di/service_locator.dart';
import 'core/localization/app_localizations.dart';
import 'core/providers/localization_provider.dart';
import 'core/services/preference_service.dart';
import 'core/theme/app_theme.dart';
import 'core/theme/app_colors.dart';
import 'core/theme/theme_manager.dart';
import 'features/splash/presentation/screens/simplified_splash_screen.dart';
import 'features/setup/presentation/screens/setup_screen.dart';
import 'features/welcome/presentation/screens/welcome_screen.dart';
import 'features/settings/presentation/screens/settings_screen.dart';
import 'features/settings/presentation/screens/csv_export_screen.dart';
import 'features/settings/presentation/screens/csv_import_screen.dart';
import 'features/settings/presentation/screens/about_screen.dart';
import 'features/settings/presentation/screens/donate_screen.dart';
import 'features/settings/presentation/screens/testing_screen.dart';
import 'features/settings/presentation/screens/appearance_screen.dart';
import 'features/settings/presentation/screens/region_screen.dart';
import 'features/settings/presentation/screens/language_screen.dart';
import 'features/settings/presentation/screens/currency_screen.dart';
import 'features/settings/presentation/screens/date_screen.dart';
import 'features/settings/presentation/screens/notifications_screen.dart';
import 'features/settings/presentation/screens/update_screen.dart';
import 'features/settings/presentation/screens/alert_threshold_screen.dart';
import 'features/settings/presentation/screens/days_advance_screen.dart';
import 'features/settings/presentation/screens/notification_types_screen.dart';
import 'features/settings/presentation/screens/reminders_screen.dart';
import 'features/settings/presentation/screens/date_format_screen.dart';
import 'features/settings/presentation/screens/time_display_screen.dart';
import 'features/settings/presentation/screens/theme_mode_screen.dart';
import 'features/settings/presentation/controllers/settings_controller.dart';
import 'features/home/<USER>/screens/riverpod_dashboard_screen.dart';
import 'features/history/presentation/screens/riverpod_history_screen.dart';

import 'features/notifications/data/notification_service.dart';
import 'features/validation/presentation/screens/validation_dashboard_screen.dart';
import 'features/cost/presentation/screens/cost_screen.dart';

void main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize timezone data
  tz.initializeTimeZones();

  // Initialize the service locator
  setupServiceLocator();

  // Initialize theme manager
  await ThemeManager.instance.initialize();

  // Initialize the notification service with error handling
  try {
    final notificationService =
        await serviceLocator.getAsync<NotificationService>();
    final bool initialized = await notificationService.initialize();
    if (!initialized) {
      debugPrint('Notification service initialization returned false');
    }
  } catch (e) {
    debugPrint('Failed to initialize notification service: $e');
    // Continue app initialization even if notifications fail
  }

  // Register the localization provider in the service locator
  serviceLocator
      .registerSingleton<LocalizationProvider>(LocalizationProvider());

  // Initialize the localization provider
  final localizationProvider = serviceLocator<LocalizationProvider>();
  await localizationProvider.loadLocale();

  runApp(const LekkyApp());
}

class LekkyApp extends StatelessWidget {
  const LekkyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ProviderScope(
      child: provider.MultiProvider(
        providers: [
          provider.ChangeNotifierProvider(create: (_) => SettingsController()),
          provider.ChangeNotifierProvider(
              create: (_) => LocalizationProvider()),
          provider.ChangeNotifierProvider(
              create: (_) => serviceLocator<PreferenceService>()),
          provider.ChangeNotifierProvider(
              create: (_) => serviceLocator<ThemeManager>()),
        ],
        child: provider.Consumer3<SettingsController, LocalizationProvider,
            ThemeManager>(
          builder: (context, settingsController, localizationProvider,
              themeManager, _) {
            return MaterialApp(
              title: 'Lekky',
              theme: AppTheme.lightTheme,
              darkTheme: AppTheme.darkTheme,
              themeMode: themeManager.currentTheme,
              debugShowCheckedModeBanner: false,
              locale: localizationProvider.locale,
              supportedLocales: const [
                Locale('en'), // English
                Locale('es'), // Spanish
                Locale('fr'), // French
                Locale('de'), // German
                Locale('it'), // Italian
                Locale('pt'), // Portuguese
                Locale('ru'), // Russian
                Locale('zh'), // Chinese
                Locale('ja'), // Japanese
                Locale('hi'), // Hindi
              ],
              localizationsDelegates: const [
                AppLocalizations.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              initialRoute: AppConstants.routeSplash,
              routes: {
                AppConstants.routeSplash: (context) =>
                    const SimplifiedSplashScreen(),
                AppConstants.routeWelcome: (context) => const WelcomeScreen(),
                AppConstants.routeSetup: (context) => const SetupScreen(),
                AppConstants.routeHome: (context) => const MainScreen(),
                '/history': (context) => const MainScreen(initialTabIndex: 1),
                '/main-settings': (context) =>
                    const MainScreen(initialTabIndex: 3),
                '/settings': (context) => const SettingsScreen(),
                '/settings/csv/export': (context) => const CsvExportScreen(),
                '/settings/csv/import': (context) => const CsvImportScreen(),
                '/settings/about': (context) => const AboutScreen(),
                '/settings/donate': (context) => const DonateScreen(),
                '/settings/donate/options': (context) => const DonateScreen(),
                '/settings/testing': (context) => const TestingScreen(),
                '/settings/appearance': (context) => const AppearanceScreen(),
                '/settings/region': (context) => const RegionScreen(),
                '/settings/region/language': (context) =>
                    const LanguageScreen(),
                '/settings/region/currency': (context) =>
                    const CurrencyScreen(),
                '/settings/date': (context) => const DateScreen(),
                '/settings/notifications': (context) =>
                    const NotificationsScreen(),
                '/settings/about/info': (context) => const AboutScreen(),
                '/settings/about/update': (context) => const UpdateScreen(),
                '/settings/about/help': (context) => const AboutScreen(),
                '/settings/notifications/threshold': (context) =>
                    const AlertThresholdScreen(),
                '/settings/notifications/days': (context) =>
                    const DaysAdvanceScreen(),
                '/settings/notifications/types': (context) =>
                    const NotificationTypesScreen(),
                '/settings/notifications/reminders': (context) =>
                    const RemindersScreen(),
                '/settings/date/format': (context) => const DateFormatScreen(),
                '/settings/date/time': (context) => const TimeDisplayScreen(),
                '/settings/appearance/theme': (context) =>
                    const ThemeModeScreen(),
                AppConstants.routeValidationDashboard: (context) =>
                    const ValidationDashboardScreen(),
              },
            );
          },
        ),
      ),
    );
  }
}

// WelcomeScreen is now implemented in features/welcome/presentation/screens/welcome_screen.dart

class MainScreen extends StatefulWidget {
  /// The initial tab index to show
  final int initialTabIndex;

  /// Constructor
  const MainScreen({super.key, this.initialTabIndex = 0});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  late int _selectedIndex;

  static const List<Widget> _screens = [
    DashboardTab(),
    HistoryTab(),
    CostTab(),
    SettingsTab(),
  ];

  late Future<void> _initNotificationsFuture;

  @override
  void initState() {
    super.initState();
    _selectedIndex = widget.initialTabIndex;

    // Initialize the notification controller
    _initNotificationsFuture = _initializeNotifications();
  }

  /// Initialize notifications - migrated to Riverpod
  Future<void> _initializeNotifications() async {
    try {
      // Notification initialization now handled by Riverpod providers
      debugPrint('Notification initialization handled by Riverpod providers');
    } catch (e) {
      debugPrint('Unexpected error in notification initialization: $e');
      // Continue app initialization even if notifications fail
    }
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  /// Get selected navigation color based on current screen and theme
  Color _getSelectedNavColor(int selectedIndex, bool isDark) {
    switch (selectedIndex) {
      case 0: // Dashboard
        return isDark ? AppColors.homeAppBarDark : AppColors.homeAppBarLight;
      case 1: // History
        return isDark
            ? AppColors.historyAppBarDark
            : AppColors.historyAppBarLight;
      case 2: // Cost
        return isDark ? AppColors.costAppBarDark : AppColors.costAppBarLight;
      case 3: // Settings
        return isDark
            ? AppColors.settingsAppBarDark
            : AppColors.settingsAppBarLight;
      default:
        return isDark ? AppColors.primaryDark : AppColors.primary;
    }
  }

  /// Switch to the History tab
  void switchToHistoryTab() {
    setState(() {
      _selectedIndex = 1; // Index of the History tab
    });
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<void>(
      future: _initNotificationsFuture,
      builder: (context, snapshot) {
        return Scaffold(
          // Remove the AppBar as we'll create a custom header in each screen
          body: _screens[_selectedIndex],
          bottomNavigationBar: provider.Consumer<PreferenceService>(
            builder: (context, prefService, _) {
              // Get currency-specific icon for Cost tab
              IconData costIcon;
              switch (prefService.currencySymbol) {
                case '£':
                  costIcon = Icons.currency_pound;
                  break;
                case '€':
                  costIcon = Icons.euro;
                  break;
                case '₦':
                  costIcon = Icons.currency_exchange;
                  break;
                default:
                  costIcon = Icons.attach_money; // Default $ icon
              }

              final isDark = Theme.of(context).brightness == Brightness.dark;

              return BottomNavigationBar(
                items: <BottomNavigationBarItem>[
                  const BottomNavigationBarItem(
                    icon: Icon(Icons.home),
                    label: 'Home',
                  ),
                  const BottomNavigationBarItem(
                    icon: Icon(Icons.history),
                    label: 'History',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(costIcon),
                    label: 'Cost',
                  ),
                  const BottomNavigationBarItem(
                    icon: Icon(Icons.settings),
                    label: 'Settings',
                  ),
                ],
                currentIndex: _selectedIndex,
                selectedItemColor: _getSelectedNavColor(_selectedIndex, isDark),
                unselectedItemColor: Theme.of(context)
                    .bottomNavigationBarTheme
                    .unselectedItemColor,
                onTap: _onItemTapped,
                type: BottomNavigationBarType.fixed,
              );
            },
          ),
        );
      },
    );
  }
}

// Dashboard tab for the main screen
class DashboardTab extends StatelessWidget {
  const DashboardTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const RiverpodDashboardScreen();
  }
}

class HistoryTab extends StatelessWidget {
  const HistoryTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const RiverpodHistoryScreen();
  }
}

class CostTab extends StatelessWidget {
  const CostTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const CostScreen();
  }
}

class SettingsTab extends StatelessWidget {
  const SettingsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return provider.ChangeNotifierProvider(
      create: (context) {
        final controller = SettingsController();
        // Check for preserved state, otherwise reset toggles
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          final hasPreservedState = await controller.hasPreservedState();
          if (hasPreservedState) {
            await controller.restoreState();
          } else {
            controller.resetAllToggles();
          }
        });
        return controller;
      },
      child: const SettingsScreen(),
    );
  }
}
