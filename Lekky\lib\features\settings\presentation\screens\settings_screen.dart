import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/app_banner.dart';
import '../../domain/models/settings_category.dart';
import '../../domain/models/settings_sub_branch.dart';
import '../controllers/settings_controller.dart';
import '../widgets/expandable_settings_category.dart';

/// Settings screen for the app
class SettingsScreen extends StatefulWidget {
  /// Constructor
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<SettingsController>(
        builder: (context, controller, _) {
          if (controller.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (controller.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 48,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'An error occurred while loading settings',
                    style: TextStyle(color: Colors.red),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      // Trigger a rebuild to retry loading
                      setState(() {});
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // App Banner with "Settings" title using gradient colors
              AppBanner(
                message: 'Settings',
                gradientColors: AppColors.getSettingsMainCardGradient(
                    Theme.of(context).brightness == Brightness.dark),
                textColor: AppColors.getAppBarTextColor('settings',
                    Theme.of(context).brightness == Brightness.dark),
              ),
              Expanded(
                child: ListView(
                  padding: const EdgeInsets.all(16.0),
                  children: [
                    // Region
                    _buildRegionCategory(context, controller, 0),

                    const SizedBox(height: 8),

                    // Alerts & Notifications
                    _buildNotificationsCategory(context, controller, 1),

                    const SizedBox(height: 8),

                    // Date Settings
                    _buildDateSettingsCategory(context, controller, 2),

                    const SizedBox(height: 8),

                    // Appearance
                    _buildAppearanceCategory(context, controller, 3),

                    const SizedBox(height: 8),

                    // Data Backup
                    _buildDataBackupCategory(context, controller, 4),

                    const SizedBox(height: 8),

                    // About
                    _buildAboutCategory(context, controller, 5),

                    const SizedBox(height: 8),

                    // Donate
                    _buildDonateCategory(context, controller, 6),

                    const SizedBox(height: 8),

                    // For Testing Only
                    _buildTestingCategory(context, controller, 7),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  // Helper methods to build each category

  Widget _buildRegionCategory(
      BuildContext context, SettingsController controller, int index) {
    final category = SettingsCategory(
      title: 'Region',
      icon: Icons.public,
      iconColor: Colors.blue,
      isEnabled: controller.isRegionEnabled,
      onToggle: controller.toggleRegion,
      currentValue: "Language, Currency",
      subBranches: [
        SettingsSubBranch(
          title: 'Language',
          icon: Icons.language,
          iconColor: Colors.blue,
          routePath: '/settings/region/language',
          currentValue: controller.language,
        ),
        SettingsSubBranch(
          title: 'Currency',
          icon: Icons.currency_exchange,
          iconColor: Colors.blue,
          routePath: '/settings/region/currency',
          currentValue: controller.currency,
        ),
      ],
    );

    return ExpandableSettingsCategory(
      category: category,
      isExpanded: controller.expandedCategoryIndex == index,
      onExpansionChanged: (expanded) {
        if (expanded) {
          controller.expandCategory(index);
        } else {
          controller.expandCategory(-1);
        }
      },
    );
  }

  Widget _buildNotificationsCategory(
      BuildContext context, SettingsController controller, int index) {
    final category = SettingsCategory(
      title: 'Alerts & Notifications',
      icon: Icons.notifications,
      iconColor: Colors.blue,
      isEnabled: controller.isNotificationsEnabled,
      onToggle: controller.toggleNotifications,
      currentValue: 'Alerts, Thresholds, Reminders',
      subBranches: [
        SettingsSubBranch(
          title: 'Alert Threshold',
          icon: Icons.warning,
          iconColor: Colors.orange,
          routePath: '/settings/notifications/threshold',
          currentValue: '£${controller.alertThreshold.toStringAsFixed(2)}',
        ),
        SettingsSubBranch(
          title: 'Days in Advance',
          icon: Icons.calendar_today,
          iconColor: Colors.blue,
          routePath: '/settings/notifications/days',
          currentValue: '${controller.daysInAdvance} days',
        ),
        const SettingsSubBranch(
          title: 'Notification Types',
          icon: Icons.notifications_active,
          iconColor: Colors.blue,
          routePath: '/settings/notifications/types',
        ),
        const SettingsSubBranch(
          title: 'Reminders',
          icon: Icons.alarm,
          iconColor: Colors.blue,
          routePath: '/settings/notifications/reminders',
        ),
      ],
    );

    return ExpandableSettingsCategory(
      category: category,
      isExpanded: controller.expandedCategoryIndex == index,
      onExpansionChanged: (expanded) {
        if (expanded) {
          controller.expandCategory(index);
        } else {
          controller.expandCategory(-1);
        }
      },
    );
  }

  Widget _buildDateSettingsCategory(
      BuildContext context, SettingsController controller, int index) {
    final category = SettingsCategory(
      title: 'Date Settings',
      icon: Icons.calendar_today,
      iconColor: Colors.blue,
      isEnabled: controller.isDateSettingsEnabled,
      onToggle: controller.toggleDateSettings,
      currentValue: 'Format, Date & Time',
      subBranches: [
        SettingsSubBranch(
          title: 'Date Format',
          icon: Icons.date_range,
          iconColor: Colors.blue,
          routePath: '/settings/date/format',
          currentValue: controller.dateFormat,
        ),
        SettingsSubBranch(
          title: 'Time Display',
          icon: Icons.access_time,
          iconColor: Colors.blue,
          routePath: '/settings/date/time',
          currentValue: controller.showTimeWithDate ? 'Shown' : 'Hidden',
        ),
      ],
    );

    return ExpandableSettingsCategory(
      category: category,
      isExpanded: controller.expandedCategoryIndex == index,
      onExpansionChanged: (expanded) {
        if (expanded) {
          controller.expandCategory(index);
        } else {
          controller.expandCategory(-1);
        }
      },
    );
  }

  Widget _buildAppearanceCategory(
      BuildContext context, SettingsController controller, int index) {
    final category = SettingsCategory(
      title: 'Appearance',
      icon: Icons.palette,
      iconColor: Colors.blue,
      isEnabled: controller.isAppearanceEnabled,
      onToggle: controller.toggleAppearance,
      currentValue: controller.themeMode.displayName,
      subBranches: [
        SettingsSubBranch(
          title: 'Theme Mode',
          icon: Icons.brightness_6,
          iconColor: Colors.blue,
          routePath: '/settings/appearance/theme',
          currentValue: controller.themeMode.displayName,
        ),
      ],
    );

    return ExpandableSettingsCategory(
      category: category,
      isExpanded: controller.expandedCategoryIndex == index,
      onExpansionChanged: (expanded) {
        if (expanded) {
          controller.expandCategory(index);
        } else {
          controller.expandCategory(-1);
        }
      },
    );
  }

  Widget _buildDataBackupCategory(
      BuildContext context, SettingsController controller, int index) {
    final category = SettingsCategory(
      title: 'Data Management',
      icon: Icons.backup,
      iconColor: Colors.blue,
      isEnabled: controller.isDataBackupEnabled,
      onToggle: controller.toggleDataBackup,
      currentValue: 'Backup, Restore',
      subBranches: [
        const SettingsSubBranch(
          title: 'Export Data',
          icon: Icons.file_download,
          iconColor: Colors.blue,
          routePath: '/settings/csv/export',
        ),
        const SettingsSubBranch(
          title: 'Import Data',
          icon: Icons.file_upload,
          iconColor: Colors.orange,
          routePath: '/settings/csv/import',
        ),
      ],
    );

    return ExpandableSettingsCategory(
      category: category,
      isExpanded: controller.expandedCategoryIndex == index,
      onExpansionChanged: (expanded) {
        if (expanded) {
          controller.expandCategory(index);
        } else {
          controller.expandCategory(-1);
        }
      },
    );
  }

  Widget _buildAboutCategory(
      BuildContext context, SettingsController controller, int index) {
    final category = SettingsCategory(
      title: 'About',
      icon: Icons.info,
      iconColor: Colors.blue,
      isEnabled: controller.isAboutEnabled,
      onToggle: controller.toggleAbout,
      currentValue: 'Info, Update, Help',
      subBranches: [
        const SettingsSubBranch(
          title: 'App Information',
          icon: Icons.info_outline,
          iconColor: Colors.blue,
          routePath: '/settings/about/info',
        ),
        const SettingsSubBranch(
          title: 'Update',
          icon: Icons.update,
          iconColor: Colors.blue,
          routePath: '/settings/about/update',
        ),
        const SettingsSubBranch(
          title: 'Help & Tips',
          icon: Icons.help,
          iconColor: Colors.blue,
          routePath: '/settings/about/help',
        ),
      ],
    );

    return ExpandableSettingsCategory(
      category: category,
      isExpanded: controller.expandedCategoryIndex == index,
      onExpansionChanged: (expanded) {
        if (expanded) {
          controller.expandCategory(index);
        } else {
          controller.expandCategory(-1);
        }
      },
    );
  }

  Widget _buildDonateCategory(
      BuildContext context, SettingsController controller, int index) {
    final category = SettingsCategory(
      title: 'Donate',
      icon: Icons.favorite,
      iconColor: Colors.red,
      isEnabled: controller.isDonateEnabled,
      onToggle: controller.toggleDonate,
      currentValue: 'Support Lekky',
      subBranches: [
        const SettingsSubBranch(
          title: 'Donation Options',
          icon: Icons.card_giftcard,
          iconColor: Colors.red,
          routePath: '/settings/donate/options',
        ),
      ],
    );

    return ExpandableSettingsCategory(
      category: category,
      isExpanded: controller.expandedCategoryIndex == index,
      onExpansionChanged: (expanded) {
        if (expanded) {
          controller.expandCategory(index);
        } else {
          controller.expandCategory(-1);
        }
      },
    );
  }

  Widget _buildTestingCategory(
      BuildContext context, SettingsController controller, int index) {
    final category = SettingsCategory(
      title: 'For Testing Only',
      icon: Icons.bug_report,
      iconColor: Colors.blue,
      isEnabled: controller.isTestingEnabled,
      onToggle: controller.toggleTesting,
      currentValue: 'Test, Settings',
      subBranches: [
        const SettingsSubBranch(
          title: 'Test Options',
          icon: Icons.build,
          iconColor: Colors.blue,
          routePath: '/settings/testing/options',
        ),
      ],
    );

    return ExpandableSettingsCategory(
      category: category,
      isExpanded: controller.expandedCategoryIndex == index,
      onExpansionChanged: (expanded) {
        if (expanded) {
          controller.expandCategory(index);
        } else {
          controller.expandCategory(-1);
        }
      },
    );
  }
}
