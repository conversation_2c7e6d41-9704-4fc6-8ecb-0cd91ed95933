{"logs": [{"outputFile": "com.lekky.app-mergeDebugResources-13:/values-am/values-am.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\684577351670909f117ab3c5c378ca3b\\transformed\\core-1.10.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,1136", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,1232"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4f3949bbe11b9a3df8476b3579cc79b0\\transformed\\browser-1.5.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,246,352", "endColumns": "95,94,105,96", "endOffsets": "146,241,347,444"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "742,838,933,1039", "endColumns": "95,94,105,96", "endOffsets": "833,928,1034,1131"}}]}]}